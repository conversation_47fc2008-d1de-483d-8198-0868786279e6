-- Version: Lua 5.3.5
-- 功能: 物料分拣系统 - 两阶段检测流程（新版本）
-- 流程: 检测区1缺陷检测 -> 传送带移动 -> 抓取 -> 检测区2背面检测 -> 分拣到装配区
-- 使用MovJExt指令控制传送带

-- ===================================================================
--                        全局变量和配置
-- ===================================================================

-- TCP通信配置
local ip = "************"
local port1 = 6005  -- 检测区1端口
local port2 = 6006  -- 检测区2端口
local err = 0
local socket1 = 0   -- 检测区1连接
local socket2 = 0   -- 检测区2连接

-- 传送带位置配置（单位：mm）
local CONVEYOR_DETECTION1_POS = 258.45      -- 检测区1位置
local CONVEYOR_DETECTION2_POS = 643.34    -- 检测区2位置
local CONVEYOR_SPEED = 80               -- 传送带运动速度比例
local CONVEYOR_ACC = 50                 -- 传送带运动加速度比例

-- 定义关键点位
local P_Camera_Pos = P7               -- 拍照位置
local P_Detection1 = P_Detection1     -- 检测区1位置
local P_Detection2 = P_Detection2     -- 检测区2位置

-- 安全高度点位（需要在DobotStudio Pro中示教）
local P_Safe_Height = P_Safe_Height  -- 安全高度点位

-- 检测区1的废料放置点
local P_Waste_Detection1 = P_Waste_Detection1  -- 检测区1废料点

-- 检测区2的6个物料放置点
local P_Material1_D2 = P_Material1_D2  -- 检测区2物料1放置点
local P_Material2_D2 = P_Material2_D2  -- 检测区2物料2放置点
local P_Material3_D2 = P_Material3_D2  -- 检测区2物料3放置点
local P_Material4_D2 = P_Material4_D2  -- 检测区2物料4放置点
local P_Material5_D2 = P_Material5_D2  -- 检测区2物料5放置点
local P_Material6_D2 = P_Material6_D2  -- 检测区2物料6放置点

-- 最终废料区（装配区1）
local P_Assembly1 = P_Waste_Detection1        -- 装配区1（最终废料区）

-- ===================================================================
--                        核心函数定义
-- ===================================================================

-- 连接检测区1视觉服务器
function connect_detection1()
    ::create_socket1::
    err, socket1 = TCPCreate(false, ip, port1)
    if err ~= 0 then
        print("无法创建检测区1 socket，正在重新连接")
        Sleep(1000)
        goto create_socket1
    end
    err = TCPStart(socket1, 0)
    if err ~= 0 then
        print("无法连接检测区1服务器，正在重新连接")
        TCPDestroy(socket1)
        Sleep(1000)
        goto create_socket1
    end
    print("检测区1 TCP连接建立成功 (端口:" .. port1 .. ")")
end

-- 连接检测区2视觉服务器
function connect_detection2()
    ::create_socket2::
    err, socket2 = TCPCreate(false, ip, port2)
    if err ~= 0 then
        print("无法创建检测区2 socket，正在重新连接")
        Sleep(1000)
        goto create_socket2
    end
    err = TCPStart(socket2, 0)
    if err ~= 0 then
        print("无法连接检测区2服务器，正在重新连接")
        TCPDestroy(socket2)
        Sleep(1000)
        goto create_socket2
    end
    print("检测区2 TCP连接建立成功 (端口:" .. port2 .. ")")
end

-- 初始化所有TCP连接
function init_tcp_connection()
    print("正在建立TCP连接...")
    connect_detection1()
    connect_detection2()
    print("所有TCP连接建立完成")
end

-- 发送指令到检测区1
function send_command_to_detection1(command)
    local msg = command
    TCPWrite(socket1, msg)
    print("已发送指令到检测区1: " .. command)
end

-- 发送指令到检测区2
function send_command_to_detection2(command)
    local msg = command
    TCPWrite(socket2, msg)
    print("已发送指令到检测区2: " .. command)
end

-- 兼容性函数
function send_vision_command(command)
    send_command_to_detection1(command)
end

-- 字符串分割函数
function split_string(str, delimiter)
    if str == nil or str == '' then
        return {}
    end
    local result = {}
    for token in string.gmatch(str, '[^' .. delimiter .. ']+') do
        table.insert(result, token)
    end
    return result
end

-- 传送带控制函数
function move_conveyor_to_position(target_pos)
    print("移动传送带到位置: " .. target_pos .. "mm")
    local option = {SpeedE = CONVEYOR_SPEED, AccE = CONVEYOR_ACC, SYNC = 1}
    MovJExt(target_pos, option)
    SyncAll()  -- 确保传送带运动完成
    print("传送带已到达位置: " .. target_pos .. "mm")
end

-- 移动传送带到检测区1
function move_conveyor_to_detection1()
    move_conveyor_to_position(CONVEYOR_DETECTION1_POS)
end

-- 移动传送带到检测区2
function move_conveyor_to_detection2()
    move_conveyor_to_position(CONVEYOR_DETECTION2_POS)
end

-- 检测区1视觉检测（获取坐标信息）
function trigger_vision_detection1()
    send_command_to_detection1("ok")
    print("等待检测区1视觉系统返回坐标...")

    local timeout_count = 0
    local max_timeout = 100  -- 最大等待次数

    while timeout_count < max_timeout do
        err, buf = TCPRead(socket1, 1000, "string")
        if err == 0 and buf and buf.buf and buf.buf ~= '' then
            print("接收到检测区1数据: " .. buf.buf)

            -- 尝试分号分隔符格式：ID;X;Y;角度
            local parts = split_string(buf.buf, ";")
            if #parts >= 4 then
                local id = parts[1]
                local x = tonumber(parts[2])
                local y = tonumber(parts[3])
                local r = tonumber(parts[4])
                if id and x and y and r then
                    return id, x, y, r
                end
            end

            -- 尝试冒号分隔符格式：ID:X:Y:角度
            parts = split_string(buf.buf, ":")
            if #parts >= 4 then
                local id = parts[1]
                local x = tonumber(parts[2])
                local y = tonumber(parts[3])
                local r = tonumber(parts[4])
                if id and x and y and r then
                    print("使用冒号格式解析成功")
                    return id, x, y, r
                end
            end

            print("数据格式错误，重新等待...")
        end
        timeout_count = timeout_count + 1
        Sleep(50)
    end

    print("检测区1检测超时")
    return nil, nil, nil, nil
end

-- 检测区2视觉检测（获取OK/NG结果）
function trigger_vision_detection2()
    -- 发送指令到检测区2（端口6006）
    send_command_to_detection2("start")
    print("等待检测区2视觉系统返回OK/NG结果...")

    local timeout_count = 0
    local max_timeout = 100  -- 最大等待次数

    while timeout_count < max_timeout do
        err, buf = TCPRead(socket2, 1000, "string")  -- 使用socket2（6006端口）
        if err == 0 and buf and buf.buf and buf.buf ~= '' then
            print("接收到检测区2结果: " .. buf.buf)
            local result = string.upper(string.gsub(buf.buf, "%s+", ""))
            if result == "OK" or result == "NG" then
                print("检测区2解析成功: " .. result)
                return result
            end
            print("结果格式错误，重新等待...")
        end
        timeout_count = timeout_count + 1
        Sleep(50)
    end

    print("检测区2检测超时")
    return nil
end

-- 执行抓取动作
function pick_material(x, y, z, r)
    print("开始抓取物料...")

    -- 定义抓取点位
    local pick_pos_safe = {coordinate = {x, y, z + 50, r}, tool = 1, user = 0}  -- 安全高度
    local pick_pos = {coordinate = {x, y, -184.72, r}, tool = 1, user = 0}            -- 抓取位置

    -- 抓取动作序列
    DO(1, 1)  -- 确保吸盘开启
    MovL(pick_pos_safe, "SYNC=1")  -- 移动到安全高度
    Sleep(200)
    MovL(pick_pos, "SYNC=1")       -- 下降到抓取位置
    Sleep(500)                     -- 等待吸附
    MovL(pick_pos_safe, "SYNC=1")  -- 提升到安全高度
    Sleep(200)

    print("物料抓取完成，保持吸盘开启状态")
    -- 注意：此处不关闭吸盘，保持抓取状态用于后续检测
end

-- 执行抓取动作（用于直接分拣的物料）
function pick_and_place_material(x, y, z, r, target_point)
    print("开始抓取并放置物料...")

    -- 定义抓取点位
    local pick_pos_safe = {coordinate = {x, y, z + 50, r}, tool = 1, user = 0}
    local pick_pos = {coordinate = {x, y, z, r}, tool = 1, user = 0}

    -- 抓取动作序列
    DO(1, 1)  -- 开启吸盘
    MovL(pick_pos_safe, "SYNC=1")
    Sleep(200)
    MovL(pick_pos, "SYNC=1")
    Sleep(500)
    MovL(pick_pos_safe, "SYNC=1")
    Sleep(200)

    -- 直接放置
    place_material(target_point, r)
end

-- 执行放置动作
function place_material(target_point, r)
    print("开始放置物料...")
    
    -- 放置动作序列
    MovJ(RP(target_point, {0, 0, 50, r}), "SYNC=1")  -- 移动到目标点上方
    Sleep(300)
    MovL(target_point, "SYNC=1")                     -- 下降到放置位置
    Sleep(200)
    DO(1, 0)                                         -- 关闭吸盘
    Sleep(300)
    DO(2, 1)                                         -- 打开喷气
    Sleep(500)
    DO(2, 0)                                         -- 关闭喷气
    MovL(RP(target_point, {0, 0, 50, r}), "SYNC=1") -- 提升到安全高度
    Sleep(200)
    
    print("物料放置完成")
end

-- ===================================================================
--                        主程序开始
-- ===================================================================

print("========================================")
print("物料分拣系统启动")
print("========================================")

-- 1. 初始化
print("正在初始化系统...")
init_tcp_connection()

-- 初始化IO端口
DO(2, 0)  -- 关闭气泵
DO(1, 1)  -- 打开吸盘（准备状态）
print("IO端口初始化完成")

-- 移动到初始位置
MovJ(P_Camera_Pos, "SYNC=1")
print("机械臂已移动到初始位置")

-- 2. 主工作循环
local cycle_count = 0
while true do
    cycle_count = cycle_count + 1
    print("========================================")
    print("开始第 " .. cycle_count .. " 次分拣循环")
    print("========================================")

    -- 步骤1: 检测区1物料识别
    print("步骤1: 触发检测区1物料识别...")
    MovJ(P_Camera_Pos, "SYNC=1")
    Sleep(500)

    local material_id, pos_x, pos_y, pos_r = trigger_vision_detection1()

    if material_id and pos_x and pos_y and pos_r then
        print(string.format("检测区1检测成功 - ID:%s, X:%.2f, Y:%.2f, R:%.2f",
              tostring(material_id), pos_x, pos_y, pos_r))

        -- 步骤2: 控制传送带移动到检测区1正前方（已完成标定板标定）
        print("步骤2: 控制传送带移动到检测区1正前方...")
        move_conveyor_to_detection1()

        -- 步骤3: 开始吸取物料
        print("步骤3: 开始吸取物料...")
        pick_material(pos_x, pos_y, 20, pos_r)

        -- 判断是否需要在第一次检测时就分拣到装配区1
        local id_num = tonumber(material_id)

        -- 检查是否为需要在检测区1直接放置废料的物料
        if material_id == "?" or id_num == nil then
            -- 未识别物料或干扰物品，在检测区1放置废料
            print("检测到未识别物料或干扰物品（ID=" .. tostring(material_id) .. "），在检测区1放置废料...")
            pick_and_place_material(pos_x, pos_y, 20, pos_r, P_Waste_Detection1)
            print("未识别物料已放置到检测区1废料点")

        else
            -- 正常物料芯片（ID 1-6），需要进行第二阶段检测
            print("检测到物料芯片ID=" .. tostring(id_num) .. "，吸住物料前往检测区2...")

            -- 步骤4: 吸取后控制传送带运动到检测区2（不松开吸盘）
            print("步骤4: 控制传送带移动到检测区2（保持吸取状态）...")
            move_conveyor_to_detection2()

            -- 移动机械臂到检测区2上方（保持抓取状态）
            MovJ(P_Detection2, "SYNC=1")
            print("机械臂已移动到检测区2上方，保持吸取状态")
            Sleep(500)

            -- 步骤5: 发送"start"触发视觉软件执行流程二（从下向上识别背面缺陷）
            print("步骤5: 发送'start'指令，触发背面缺陷检测...")
            local detection_result = trigger_vision_detection2()

            -- 步骤6: 根据OK/NG结果进行分拣
            if detection_result then
                print("背面缺陷检测结果: " .. detection_result)

                if detection_result == "OK" then
                    -- 背面无缺陷，直接放置到装配区2对应ID槽
                    print("背面检测OK，直接放置到装配区2对应ID槽...")

                    if id_num == 1 then
                        place_material(P_Material1_D2, pos_r)
                        print("物料ID=1已放置到装配区2位置1")
                    elseif id_num == 2 then
                        place_material(P_Material2_D2, pos_r)
                        print("物料ID=2已放置到装配区2位置2")
                    elseif id_num == 3 then
                        place_material(P_Material3_D2, pos_r)
                        print("物料ID=3已放置到装配区2位置3")
                    elseif id_num == 4 then
                        place_material(P_Material4_D2, pos_r)
                        print("物料ID=4已放置到装配区2位置4")
                    elseif id_num == 5 then
                        place_material(P_Material5_D2, pos_r)
                        print("物料ID=5已放置到装配区2位置5")
                    elseif id_num == 6 then
                        place_material(P_Material6_D2, pos_r)
                        print("物料ID=6已放置到装配区2位置6")
                    else
                        -- 未知ID，移到装配区1（废料区）
                        place_material(P_Assembly1, pos_r)
                        print("未知ID物料已移到装配区1（废料区）")
                    end

                    -- 放置完成后，先抬高机械臂再返回检测区1
                    print("物料放置完成，先抬高机械臂到安全高度...")
                    MovJ(P_Safe_Height, "SYNC=1")  -- 先移动到安全高度（检测区2上方100mm）
                    Sleep(300)

                    print("机械臂已抬高，现在返回检测区1开始下一轮分拣...")
                    move_conveyor_to_detection1()
                    MovJ(P_Detection1, "SYNC=1")
                    print("已返回检测区1，准备开始下一轮分拣")
                    Sleep(500)

                else  -- NG
                    -- 背面有缺陷，先抬高机械臂，然后返回检测区1，最后移到装配区1（废料区）
                    print("背面检测NG，先抬高机械臂到安全高度...")
                    MovJ(P_Safe_Height, "SYNC=1")  -- 先移动到安全高度（检测区2上方100mm）
                    Sleep(300)

                    print("机械臂已抬高，现在返回检测区1...")
                    -- 返回检测区1
                    move_conveyor_to_detection1()
                    MovJ(P_Detection1, "SYNC=1")
                    print("已返回检测区1，现在移到装配区1（废料区）...")
                    Sleep(500)

                    -- 移到装配区1（废料区）
                    place_material(P_Assembly1, pos_r)
                    print("NG物料已移到装配区1（废料区）")
                end
            else
                -- 检测失败，先抬高机械臂，然后返回检测区1，最后默认移到装配区1（废料区）
                print("背面缺陷检测失败，先抬高机械臂到安全高度...")
                MovJ(P_Safe_Height, "SYNC=1")  -- 先移动到安全高度（检测区2上方100mm）
                Sleep(300)

                print("机械臂已抬高，现在返回检测区1...")
                -- 返回检测区1
                move_conveyor_to_detection1()
                MovJ(P_Detection1, "SYNC=1")
                print("已返回检测区1，现在移到装配区1（废料区）...")
                Sleep(500)

                -- 默认移到装配区1（废料区）
                place_material(P_Assembly1, pos_r)
                print("检测失败物料已移到装配区1（废料区）")
            end
        end
        
    else
        print("检测区1检测失败，跳过本次循环")
    end
    
    print("第 " .. cycle_count .. " 次分拣循环完成")
    print("等待下一次循环...")
    Sleep(2000)  -- 等待2秒
end
