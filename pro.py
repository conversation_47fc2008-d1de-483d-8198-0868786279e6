# =================================================================================
#  机器人视觉控制系统 - V16.0 (集成传送带前进/后退控制)
# =================================================================================
import customtkinter as ctk
import socket
import threading
import queue
import time
from PIL import Image, ImageTk
import os
import ctypes
import requests
import json
import subprocess
import re
from position_manager import position_manager

# --- 1. 全局配置 ---
ROBOT_IP = "***********"
PYTHON_PC_IP = "*************"
DASHBOARD_PORT = 29999
MOTION_PORT = 30003
VISION_SERVER_PORT = 6005
GRIPPER_IO_INDEX = 1
VISION_TRIGGER_PORT = 6006
VISION_TRIGGER_CMD = "TRIGGER"

# ▼▼▼▼▼ 【DobotStudio Pro 滑轨控制器】 ▼▼▼▼▼
class DobotSlideRailController:
    """DobotStudio Pro 滑轨精确控制器"""

    def __init__(self, robot_ip="***********", http_port=22000, tcp_port=30003):
        self.robot_ip = robot_ip
        self.http_port = http_port
        self.tcp_port = tcp_port
        self.http_base_url = f"http://{robot_ip}:{http_port}"
        self.current_position = 0.0
        self.slide_range = 850.0  # 滑轨总长度
        self.is_connected = False

    def connect(self):
        """连接到DobotStudio Pro"""
        try:
            # 测试HTTP连接
            response = requests.get(f"{self.http_base_url}/connection/state", timeout=3)
            if response.status_code == 200:
                self.is_connected = True
                return True
        except:
            pass

        # 如果HTTP失败，尝试TCP连接测试
        try:
            test_socket = socket.create_connection((self.robot_ip, self.tcp_port), timeout=3)
            test_socket.close()
            self.is_connected = True
            return True
        except:
            self.is_connected = False
            return False

    def move_to_absolute_position(self, position_mm, speed=50, accel=50):
        """移动到绝对位置"""
        if not self.is_connected:
            return False

        if not (0 <= position_mm <= self.slide_range):
            return False

        try:
            # 使用MovJExt指令控制扩展轴
            cmd = f"MovJExt({position_mm},{{SpeedE={speed},AccE={accel},SYNC=1}})"
            success = self._send_command_tcp(cmd)

            if success:
                self.current_position = position_mm

            return success
        except Exception as e:
            print(f"移动到绝对位置失败: {e}")
            return False

    def move_relative(self, distance_mm, speed=50, accel=50):
        """相对移动"""
        target_position = self.current_position + distance_mm
        return self.move_to_absolute_position(target_position, speed, accel)

    def get_current_position(self):
        """获取当前位置"""
        # 这里可以实现位置查询逻辑
        # 目前返回内部记录的位置
        return self.current_position

    def emergency_stop(self):
        """急停"""
        try:
            cmd = "Pause()"
            return self._send_command_tcp(cmd)
        except:
            return False

    def _send_command_tcp(self, command):
        """通过TCP发送指令"""
        try:
            with socket.create_connection((self.robot_ip, self.tcp_port), timeout=5) as sock:
                full_cmd = command + "\n"
                sock.sendall(full_cmd.encode('utf-8'))
                response = sock.recv(1024).decode('utf-8').strip()
                # 检查响应是否成功（通常成功返回包含'0'）
                return '0' in response.split(',')[0] if response else False
        except Exception as e:
            print(f"TCP指令发送失败: {e}")
            return False

    def _send_command_http(self, command):
        """通过HTTP发送指令"""
        try:
            payload = {"command": command}
            response = requests.post(f"{self.http_base_url}/interface/command",
                                   json=payload, timeout=5)
            return response.status_code == 200
        except Exception as e:
            print(f"HTTP指令发送失败: {e}")
            return False
# ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

def send_cmd(sock, cmd, log_prefix="CMD"):
    # (此函数无变化)
    try:
        full_cmd = cmd + "\n"
        print(f"[{log_prefix}] SND: {full_cmd.strip()}")
        sock.sendall(full_cmd.encode('utf-8'))
        response = sock.recv(1024).decode('utf-8').strip()
        print(f"[{log_prefix}] RCV: {response}")
        parts = response.split(',')
        error_id_str = parts[0]
        if error_id_str == '0': return True
        else:
            print(f"❌ 指令 '{cmd}' 失败，错误码: {error_id_str}")
            return False
    except socket.error as e:
        print(f"❌ 发送指令 '{cmd}' 时网络错误: {e}")
        return False

class RobotControlApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("机器人工件分拣控制系统 (MG400) - V18.0")
        self.geometry("1200x800")  # 增加宽度，减少高度

        # 滑轨控制相关变量
        self.current_conveyor_position = 0.0
        self.conveyor_total_length = 850.0  # 更新为实际滑轨长度
        self.target_position = 0.0

        # 网络连接
        self.dashboard_socket = None
        self.motion_socket = None
        self.vision_queue = queue.Queue()
        self.is_robot_connected = False

        # 工件分拣控制相关变量
        self.sorting_process = None
        self.is_sorting_running = False
        self.conveyor_pro_project_path = "传送带pro"

        # 初始化DobotStudio Pro滑轨控制器
        self.slide_controller = DobotSlideRailController(ROBOT_IP)

        # UI和线程初始化
        self.create_widgets()
        self.update_conveyor_display()
        self.update_position_record_display()  # 更新位置记录显示
        self.vision_thread = threading.Thread(target=self.vision_listener_thread, daemon=True)
        self.vision_thread.start()
        self.process_vision_queue()
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 权限检查
        try:
            is_admin = os.getuid() == 0
        except AttributeError:
            is_admin = ctypes.windll.shell32.IsUserAnAdmin() != 0
        if is_admin:
            self.log("✅ 程序正以【管理员权限】运行。", "lightgreen")
        else:
            self.log("⚠️ 程序正以【普通用户权限】运行。", "orange")

        # 尝试连接滑轨控制器
        self.log("🔗 正在连接DobotStudio Pro滑轨控制器...", "cyan")
        if self.slide_controller.connect():
            self.log("✅ 滑轨控制器连接成功！", "green")
        else:
            self.log("⚠️ 滑轨控制器连接失败，将使用传统控制模式。", "orange")

    def create_widgets(self):
        # 主布局框架
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.pack(padx=5, pady=5, fill="both", expand=True)
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_columnconfigure(1, weight=1)
        self.main_frame.grid_rowconfigure(0, weight=1)

        # 左侧滚动控制面板
        self.left_frame = ctk.CTkScrollableFrame(self.main_frame, width=580, height=750,
                                                fg_color="transparent")
        self.left_frame.grid(row=0, column=0, padx=5, pady=5, sticky="nsew")
        
        # ... (机器人人机控制UI代码无变化)
        manual_control_frame = ctk.CTkFrame(self.left_frame)
        manual_control_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(manual_control_frame, text="机器人人机控制", font=ctk.CTkFont(size=16, weight="bold")).grid(row=0, column=0, columnspan=2, pady=10)
        btn_x_plus = ctk.CTkButton(manual_control_frame, text="X+"); btn_x_minus = ctk.CTkButton(manual_control_frame, text="X-"); btn_y_plus = ctk.CTkButton(manual_control_frame, text="Y+"); btn_y_minus = ctk.CTkButton(manual_control_frame, text="Y-"); btn_z_plus = ctk.CTkButton(manual_control_frame, text="Z+"); btn_z_minus = ctk.CTkButton(manual_control_frame, text="Z-")
        btn_x_plus.grid(row=1, column=0, padx=5, pady=5, sticky="ew"); btn_x_minus.grid(row=1, column=1, padx=5, pady=5, sticky="ew"); btn_y_plus.grid(row=2, column=0, padx=5, pady=5, sticky="ew"); btn_y_minus.grid(row=2, column=1, padx=5, pady=5, sticky="ew"); btn_z_plus.grid(row=3, column=0, padx=5, pady=5, sticky="ew"); btn_z_minus.grid(row=3, column=1, padx=5, pady=5, sticky="ew")
        btn_x_plus.bind("<ButtonPress-1>", lambda event: self.start_jog("X+")); btn_x_minus.bind("<ButtonPress-1>", lambda event: self.start_jog("X-")); btn_y_plus.bind("<ButtonPress-1>", lambda event: self.start_jog("Y+")); btn_y_minus.bind("<ButtonPress-1>", lambda event: self.start_jog("Y-")); btn_z_plus.bind("<ButtonPress-1>", lambda event: self.start_jog("Z+")); btn_z_minus.bind("<ButtonPress-1>", lambda event: self.start_jog("Z-"))
        for btn in [btn_x_plus, btn_x_minus, btn_y_plus, btn_y_minus, btn_z_plus, btn_z_minus]: btn.bind("<ButtonRelease-1>", self.stop_jog)
        self.btn_home = ctk.CTkButton(manual_control_frame, text="回原点", command=self.go_home); self.btn_home.grid(row=4, column=0, columnspan=2, pady=10, padx=5, sticky="ew")

        # ▼▼▼▼▼ 【精准滑轨控制UI - V17.0 全面升级】 ▼▼▼▼▼
        conveyor_frame = ctk.CTkFrame(self.left_frame)
        conveyor_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(conveyor_frame, text="精准滑轨控制", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)

        # 位置显示区域
        pos_display_frame = ctk.CTkFrame(conveyor_frame)
        pos_display_frame.pack(pady=5, padx=5, fill="x")

        # 当前位置显示
        current_pos_frame = ctk.CTkFrame(pos_display_frame, fg_color="transparent")
        current_pos_frame.pack(pady=2, fill="x")
        ctk.CTkLabel(current_pos_frame, text="当前位置:", font=ctk.CTkFont(size=12)).pack(side="left")
        self.label_conveyor_pos = ctk.CTkLabel(current_pos_frame, text="0.00 mm",
                                              font=ctk.CTkFont(size=14, weight="bold"), text_color="lightgreen")
        self.label_conveyor_pos.pack(side="left", padx=5)

        # 目标位置显示
        target_pos_frame = ctk.CTkFrame(pos_display_frame, fg_color="transparent")
        target_pos_frame.pack(pady=2, fill="x")
        ctk.CTkLabel(target_pos_frame, text="目标位置:", font=ctk.CTkFont(size=12)).pack(side="left")
        self.label_target_pos = ctk.CTkLabel(target_pos_frame, text="-- mm",
                                            font=ctk.CTkFont(size=14), text_color="orange")
        self.label_target_pos.pack(side="left", padx=5)

        # 进度条
        self.progress_conveyor = ctk.CTkProgressBar(conveyor_frame)
        self.progress_conveyor.pack(pady=5, padx=5, fill="x")

        # 步进控制区域
        step_control_frame = ctk.CTkFrame(conveyor_frame)
        step_control_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(step_control_frame, text="步进控制", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        step_input_frame = ctk.CTkFrame(step_control_frame, fg_color="transparent")
        step_input_frame.pack(pady=5, fill="x")
        ctk.CTkLabel(step_input_frame, text="步进距离(mm):").pack(side="left", padx=(0, 5))
        self.entry_conveyor_step_dist = ctk.CTkEntry(step_input_frame, placeholder_text="例如: 50", width=100)
        self.entry_conveyor_step_dist.pack(side="left", padx=5)
        self.entry_conveyor_step_dist.insert(0, "50")

        step_btn_frame = ctk.CTkFrame(step_control_frame, fg_color="transparent")
        step_btn_frame.pack(pady=5, fill="x")
        self.btn_conveyor_bwd = ctk.CTkButton(step_btn_frame, text="◀ 后退", command=lambda: self.move_conveyor_step(-1), width=80)
        self.btn_conveyor_bwd.pack(side="left", padx=5)
        self.btn_conveyor_fwd = ctk.CTkButton(step_btn_frame, text="前进 ▶", command=lambda: self.move_conveyor_step(1), width=80)
        self.btn_conveyor_fwd.pack(side="left", padx=5)

        # 绝对位置控制区域
        abs_control_frame = ctk.CTkFrame(conveyor_frame)
        abs_control_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(abs_control_frame, text="绝对位置控制", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        abs_input_frame = ctk.CTkFrame(abs_control_frame, fg_color="transparent")
        abs_input_frame.pack(pady=5, fill="x")
        ctk.CTkLabel(abs_input_frame, text="目标位置(mm):").pack(side="left", padx=(0, 5))
        self.entry_target_position = ctk.CTkEntry(abs_input_frame, placeholder_text="0-850", width=100)
        self.entry_target_position.pack(side="left", padx=5)
        self.btn_move_to_position = ctk.CTkButton(abs_input_frame, text="移动到此位置",
                                                 command=self.move_to_absolute_position, width=120)
        self.btn_move_to_position.pack(side="left", padx=5)

        # 快速位置按钮
        quick_pos_frame = ctk.CTkFrame(abs_control_frame, fg_color="transparent")
        quick_pos_frame.pack(pady=5, fill="x")
        quick_positions = [0, 200, 400, 600, 850]
        for pos in quick_positions:
            btn = ctk.CTkButton(quick_pos_frame, text=f"{pos}mm", width=60,
                               command=lambda p=pos: self.move_to_quick_position(p))
            btn.pack(side="left", padx=2)

        # 标定和设置区域
        calib_frame = ctk.CTkFrame(conveyor_frame)
        calib_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(calib_frame, text="标定与设置", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        calib_btn_frame = ctk.CTkFrame(calib_frame, fg_color="transparent")
        calib_btn_frame.pack(pady=5, fill="x")
        self.btn_calib_conveyor = ctk.CTkButton(calib_btn_frame, text="标定零点",
                                               command=self.calibrate_conveyor_zero, width=80)
        self.btn_calib_conveyor.pack(side="left", padx=5)
        self.btn_find_limits = ctk.CTkButton(calib_btn_frame, text="检测限位",
                                            command=self.find_slide_limits, width=80)
        self.btn_find_limits.pack(side="left", padx=5)
        self.btn_emergency_stop = ctk.CTkButton(calib_btn_frame, text="急停",
                                               command=self.emergency_stop, width=60,
                                               fg_color="red", hover_color="darkred")
        self.btn_emergency_stop.pack(side="left", padx=5)
        # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

        # ▼▼▼▼▼ 【位置记录和验证系统 - V17.1 新增】 ▼▼▼▼▼
        position_record_frame = ctk.CTkFrame(self.left_frame)
        position_record_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(position_record_frame, text="位置记录和验证系统",
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)

        # 记录进度显示
        progress_frame = ctk.CTkFrame(position_record_frame)
        progress_frame.pack(pady=5, padx=5, fill="x")

        progress_info_frame = ctk.CTkFrame(progress_frame, fg_color="transparent")
        progress_info_frame.pack(pady=5, fill="x")
        ctk.CTkLabel(progress_info_frame, text="记录进度:", font=ctk.CTkFont(size=12)).pack(side="left")
        self.label_record_progress = ctk.CTkLabel(progress_info_frame, text="0/2",
                                                 font=ctk.CTkFont(size=12, weight="bold"), text_color="orange")
        self.label_record_progress.pack(side="left", padx=5)

        self.progress_record = ctk.CTkProgressBar(progress_frame)
        self.progress_record.pack(pady=5, padx=5, fill="x")
        self.progress_record.set(0)

        # 两个门坐标点位记录区域
        zones_frame = ctk.CTkFrame(position_record_frame)
        zones_frame.pack(pady=5, padx=5, fill="x")

        # 门坐标区域
        door_frame = ctk.CTkFrame(zones_frame)
        door_frame.pack(pady=5, fill="x")
        ctk.CTkLabel(door_frame, text="门坐标位置", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        # 门坐标1
        door1_frame = ctk.CTkFrame(door_frame, fg_color="transparent")
        door1_frame.pack(pady=2, fill="x")
        self.label_door1_status = ctk.CTkLabel(door1_frame, text="门坐标1: 未记录",
                                             font=ctk.CTkFont(size=12), text_color="gray")
        self.label_door1_status.pack(side="left")
        self.btn_record_door1 = ctk.CTkButton(door1_frame, text="记录门坐标1", width=100,
                                            command=lambda: self.record_position("door_coord_1"))
        self.btn_record_door1.pack(side="right", padx=5)
        self.btn_verify_door1 = ctk.CTkButton(door1_frame, text="验证", width=60,
                                            command=lambda: self.verify_position("door_coord_1"),
                                            state="disabled")
        self.btn_verify_door1.pack(side="right", padx=2)

        # 门坐标2
        door2_frame = ctk.CTkFrame(door_frame, fg_color="transparent")
        door2_frame.pack(pady=2, fill="x")
        self.label_door2_status = ctk.CTkLabel(door2_frame, text="门坐标2: 未记录",
                                             font=ctk.CTkFont(size=12), text_color="gray")
        self.label_door2_status.pack(side="left")
        self.btn_record_door2 = ctk.CTkButton(door2_frame, text="记录门坐标2", width=100,
                                            command=lambda: self.record_position("door_coord_2"))
        self.btn_record_door2.pack(side="right", padx=5)
        self.btn_verify_door2 = ctk.CTkButton(door2_frame, text="验证", width=60,
                                            command=lambda: self.verify_position("door_coord_2"),
                                            state="disabled")
        self.btn_verify_door2.pack(side="right", padx=2)

        # 批量操作按钮
        batch_frame = ctk.CTkFrame(position_record_frame)
        batch_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(batch_frame, text="批量操作", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        batch_btn_frame = ctk.CTkFrame(batch_frame, fg_color="transparent")
        batch_btn_frame.pack(pady=5, fill="x")

        self.btn_verify_all = ctk.CTkButton(batch_btn_frame, text="验证所有位置", width=100,
                                           command=self.verify_all_positions, state="disabled")
        self.btn_verify_all.pack(side="left", padx=5)

        self.btn_clear_all = ctk.CTkButton(batch_btn_frame, text="清除所有记录", width=100,
                                          command=self.clear_all_records, fg_color="orange", hover_color="darkorange")
        self.btn_clear_all.pack(side="left", padx=5)

        self.btn_export_positions = ctk.CTkButton(batch_btn_frame, text="导出位置", width=80,
                                                 command=self.export_positions)
        self.btn_export_positions.pack(side="left", padx=5)

        # 验证结果显示
        self.label_verify_result = ctk.CTkLabel(position_record_frame, text="",
                                               font=ctk.CTkFont(size=12))
        self.label_verify_result.pack(pady=5)
        # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

        # ▼▼▼▼▼ 【工件分拣控制系统 - 新增】 ▼▼▼▼▼
        sorting_control_frame = ctk.CTkFrame(self.left_frame)
        sorting_control_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(sorting_control_frame, text="工件分拣控制系统",
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)

        # 分拣状态显示
        status_frame = ctk.CTkFrame(sorting_control_frame)
        status_frame.pack(pady=5, padx=5, fill="x")

        status_info_frame = ctk.CTkFrame(status_frame, fg_color="transparent")
        status_info_frame.pack(pady=5, fill="x")
        ctk.CTkLabel(status_info_frame, text="分拣状态:", font=ctk.CTkFont(size=12)).pack(side="left")
        self.label_sorting_status = ctk.CTkLabel(status_info_frame, text="未启动",
                                               font=ctk.CTkFont(size=12, weight="bold"), text_color="gray")
        self.label_sorting_status.pack(side="left", padx=5)

        # 分拣控制按钮
        control_frame = ctk.CTkFrame(sorting_control_frame)
        control_frame.pack(pady=5, padx=5, fill="x")

        control_btn_frame = ctk.CTkFrame(control_frame, fg_color="transparent")
        control_btn_frame.pack(pady=5, fill="x")

        self.btn_sync_positions = ctk.CTkButton(control_btn_frame, text="同步门坐标", width=100,
                                              command=self.sync_door_coordinates,
                                              fg_color="blue", hover_color="darkblue")
        self.btn_sync_positions.pack(side="left", padx=5)

        self.btn_start_sorting = ctk.CTkButton(control_btn_frame, text="开始分拣", width=100,
                                             command=self.start_material_sorting,
                                             fg_color="green", hover_color="darkgreen")
        self.btn_start_sorting.pack(side="left", padx=5)

        self.btn_stop_sorting = ctk.CTkButton(control_btn_frame, text="停止分拣", width=100,
                                            command=self.stop_material_sorting,
                                            fg_color="red", hover_color="darkred",
                                            state="disabled")
        self.btn_stop_sorting.pack(side="left", padx=5)

        # 分拣信息显示
        info_frame = ctk.CTkFrame(sorting_control_frame)
        info_frame.pack(pady=5, padx=5, fill="x")

        self.label_sorting_info = ctk.CTkLabel(info_frame, text="请先记录门坐标1和门坐标2，然后点击'同步门坐标'",
                                             font=ctk.CTkFont(size=11), text_color="orange")
        self.label_sorting_info.pack(pady=5)
        # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

        # ... (后续UI代码无变化)
        vision_control_frame = ctk.CTkFrame(self.left_frame); vision_control_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(vision_control_frame, text="视觉控制", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)
        ctk.CTkButton(vision_control_frame, text="拍照", command=self.trigger_vision_capture).pack(pady=5, padx=5, fill="x")
        connect_frame = ctk.CTkFrame(self.left_frame); connect_frame.pack(pady=20, padx=10, fill="x", side="bottom")
        self.connect_label = ctk.CTkLabel(connect_frame, text="机器人未连接", text_color="orange"); self.connect_label.pack(side="left", padx=10)
        self.btn_connect = ctk.CTkButton(connect_frame, text="连接机器人", command=self.handle_connect_button_click); self.btn_connect.pack(side="right", padx=10)
        self.auto_run_switch = ctk.CTkSwitch(connect_frame, text="自动抓取", onvalue=True, offvalue=False); self.auto_run_switch.pack(side="right", padx=10)
        # 右侧监控和日志面板
        self.right_frame = ctk.CTkFrame(self.main_frame, width=580)
        self.right_frame.grid(row=0, column=1, padx=5, pady=5, sticky="nsew")
        ctk.CTkLabel(self.right_frame, text="监控界面", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)
        self.image_display_label = ctk.CTkLabel(self.right_frame, text="[等待视觉软件发送图像...]", bg_color="grey30", height=300); self.image_display_label.pack(pady=10, padx=10, fill="both", expand=True)
        ctk.CTkLabel(self.right_frame, text="信息显示/日志", font=ctk.CTkFont(size=14)).pack()
        self.log_textbox = ctk.CTkTextbox(self.right_frame, state="disabled", height=200); self.log_textbox.pack(pady=10, padx=10, fill="both", expand=True)


    # ▼▼▼▼▼ 【精准滑轨控制函数 - V17.0 全面升级】 ▼▼▼▼▼
    def update_conveyor_display(self):
        """刷新滑轨位置的UI显示"""
        self.label_conveyor_pos.configure(text=f"{self.current_conveyor_position:.2f} mm")
        self.label_target_pos.configure(text=f"{self.target_position:.2f} mm")
        progress = self.current_conveyor_position / self.conveyor_total_length if self.conveyor_total_length > 0 else 0
        self.progress_conveyor.set(max(0, min(1, progress)))

    def calibrate_conveyor_zero(self):
        """标定当前位置为零点"""
        self.log("⚙️ 正在标定滑轨零点...", "yellow")
        self.current_conveyor_position = 0.0
        self.target_position = 0.0
        self.slide_controller.current_position = 0.0
        self.update_conveyor_display()
        self.log("✅ 滑轨零点标定完成。", "green")

    def move_conveyor_step(self, direction):
        """按步进距离移动滑轨 (1: 前进, -1: 后退)"""
        if not self.is_robot_connected:
            self.log("⚠️ 请先连接机器人", "orange")
            return

        step_dist_str = self.entry_conveyor_step_dist.get()
        try:
            step_distance = abs(float(step_dist_str))

            if direction > 0:
                target_position = self.current_conveyor_position + step_distance
                log_action = "前进"
            else:
                target_position = self.current_conveyor_position - step_distance
                log_action = "后退"

            # 检查范围限制
            if not (0 <= target_position <= self.conveyor_total_length):
                self.log(f"❌ 目标位置 {target_position:.2f} mm 超出范围 (0-{self.conveyor_total_length} mm)", "red")
                return

            self.target_position = target_position
            self.update_conveyor_display()
            self.log(f"🎯 准备{log_action} {step_distance} mm 到位置 {target_position:.2f} mm...", "cyan")

            # 使用DobotStudio Pro精确控制
            if self.slide_controller.is_connected:
                success = self.slide_controller.move_to_absolute_position(target_position)
            else:
                # 回退到传统控制模式
                distance_to_move = step_distance * direction
                cmd = f"MovJExt({distance_to_move},{{SYNC=1}})"
                success = send_cmd(self.motion_socket, cmd, "MOT")

            if success:
                self.current_conveyor_position = target_position
                self.update_conveyor_display()
                self.log(f"✅ 滑轨精确移动完成。当前位置: {self.current_conveyor_position:.2f} mm", "green")
            else:
                self.log("❌ 滑轨移动失败。", "red")

        except ValueError:
            self.log(f"❌ 无效输入: '{step_dist_str}' 不是有效的步进距离。", "red")
        except Exception as e:
            self.log(f"❌ 移动滑轨时发生错误: {e}", "red")

    def move_to_absolute_position(self):
        """移动到绝对位置"""
        if not self.is_robot_connected:
            self.log("⚠️ 请先连接机器人", "orange")
            return

        target_str = self.entry_target_position.get()
        try:
            target_position = float(target_str)

            # 检查范围限制
            if not (0 <= target_position <= self.conveyor_total_length):
                self.log(f"❌ 目标位置 {target_position:.2f} mm 超出范围 (0-{self.conveyor_total_length} mm)", "red")
                return

            self.target_position = target_position
            self.update_conveyor_display()
            self.log(f"🎯 正在移动到绝对位置 {target_position:.2f} mm...", "cyan")

            # 使用DobotStudio Pro精确控制
            if self.slide_controller.is_connected:
                success = self.slide_controller.move_to_absolute_position(target_position)
            else:
                # 回退到传统控制模式
                distance_to_move = target_position - self.current_conveyor_position
                cmd = f"MovJExt({distance_to_move},{{SYNC=1}})"
                success = send_cmd(self.motion_socket, cmd, "MOT")

            if success:
                self.current_conveyor_position = target_position
                self.update_conveyor_display()
                self.log(f"✅ 已到达目标位置: {self.current_conveyor_position:.2f} mm", "green")
            else:
                self.log("❌ 移动到绝对位置失败。", "red")

        except ValueError:
            self.log(f"❌ 无效输入: '{target_str}' 不是有效的位置值。", "red")
        except Exception as e:
            self.log(f"❌ 移动到绝对位置时发生错误: {e}", "red")

    def move_to_quick_position(self, position):
        """快速移动到预设位置"""
        self.entry_target_position.delete(0, 'end')
        self.entry_target_position.insert(0, str(position))
        self.move_to_absolute_position()

    def find_slide_limits(self):
        """检测滑轨限位"""
        self.log("🔍 开始检测滑轨限位...", "yellow")
        self.log("⚠️ 此功能需要硬件限位开关支持", "orange")
        # 这里可以实现限位检测逻辑
        # 目前只是提示功能

    def emergency_stop(self):
        """紧急停止"""
        self.log("🛑 执行紧急停止！", "red")
        if self.slide_controller.is_connected:
            self.slide_controller.emergency_stop()
        if self.motion_socket:
            send_cmd(self.motion_socket, "Pause()", "MOT")
        self.log("✅ 紧急停止执行完成。", "orange")
    # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

    # ▼▼▼▼▼ 【位置记录和验证功能 - V17.1 新增】 ▼▼▼▼▼
    def update_position_record_display(self):
        """更新位置记录显示"""
        recorded_count = position_manager.get_all_recorded_count()
        total_count = 2

        # 更新进度显示
        self.label_record_progress.configure(text=f"{recorded_count}/{total_count}")
        progress = recorded_count / total_count
        self.progress_record.set(progress)

        # 更新各区域状态显示
        zones = [
            ("door_coord_1", self.label_door1_status, self.btn_verify_door1),
            ("door_coord_2", self.label_door2_status, self.btn_verify_door2)
        ]

        for zone_key, status_label, verify_btn in zones:
            zone_name = position_manager.get_zone_display_name(zone_key)

            if position_manager.is_recorded(zone_key):
                position = position_manager.get_position(zone_key)
                record_time = position_manager.get_record_time(zone_key)
                status_label.configure(
                    text=f"{zone_name}: {position:.2f}mm ({record_time})",
                    text_color="lightgreen"
                )
                verify_btn.configure(state="normal")
            else:
                status_label.configure(
                    text=f"{zone_name}: 未记录",
                    text_color="gray"
                )
                verify_btn.configure(state="disabled")

        # 更新批量验证按钮状态
        if recorded_count > 0:
            self.btn_verify_all.configure(state="normal")
        else:
            self.btn_verify_all.configure(state="disabled")

    def record_position(self, zone_key: str):
        """记录指定区域的位置"""
        zone_name = position_manager.get_zone_display_name(zone_key)
        current_pos = self.current_conveyor_position

        self.log(f"📍 正在记录{zone_name}位置: {current_pos:.2f} mm...", "cyan")

        success = position_manager.record_position(zone_key, current_pos)

        if success:
            self.log(f"✅ {zone_name}位置记录成功: {current_pos:.2f} mm", "green")
            self.update_position_record_display()

            # 检查是否所有位置都已记录
            if position_manager.is_all_recorded():
                self.log("🎉 所有位置记录完成！可以开始验证。", "lightgreen")
        else:
            self.log(f"❌ {zone_name}位置记录失败", "red")

    def verify_position(self, zone_key: str):
        """验证指定区域的位置"""
        if not self.is_robot_connected:
            self.log("⚠️ 请先连接机器人", "orange")
            return

        zone_name = position_manager.get_zone_display_name(zone_key)
        expected_position = position_manager.get_position(zone_key)

        if expected_position is None:
            self.log(f"❌ {zone_name}尚未记录位置", "red")
            return

        self.log(f"🎯 验证{zone_name}位置，移动到 {expected_position:.2f} mm...", "cyan")

        # 移动到记录的位置
        if self.slide_controller.is_connected:
            success = self.slide_controller.move_to_absolute_position(expected_position)
        else:
            distance_to_move = expected_position - self.current_conveyor_position
            cmd = f"MovJExt({distance_to_move},{{SYNC=1}})"
            success = send_cmd(self.motion_socket, cmd, "MOT")

        if success:
            # 更新当前位置
            self.current_conveyor_position = expected_position
            self.update_conveyor_display()

            # 验证位置精度
            actual_position = self.current_conveyor_position
            is_valid, error = position_manager.validate_position(zone_key, actual_position)

            if is_valid:
                self.log(f"✅ {zone_name}位置验证成功！误差: {error:.2f} mm", "green")
                self.label_verify_result.configure(
                    text=f"✅ {zone_name}: 期望 {expected_position:.2f}mm, 实际 {actual_position:.2f}mm, 误差 {error:.2f}mm",
                    text_color="green"
                )
            else:
                self.log(f"⚠️ {zone_name}位置验证警告！误差: {error:.2f} mm (超出±{position_manager.tolerance_mm}mm)", "orange")
                self.label_verify_result.configure(
                    text=f"⚠️ {zone_name}: 期望 {expected_position:.2f}mm, 实际 {actual_position:.2f}mm, 误差 {error:.2f}mm",
                    text_color="orange"
                )
        else:
            self.log(f"❌ 移动到{zone_name}失败", "red")
            self.label_verify_result.configure(
                text=f"❌ {zone_name}验证失败：移动失败",
                text_color="red"
            )

    def verify_all_positions(self):
        """验证所有已记录的位置"""
        if not self.is_robot_connected:
            self.log("⚠️ 请先连接机器人", "orange")
            return

        self.log("🔍 开始验证所有已记录位置...", "cyan")

        zones = ["door_coord_1", "door_coord_2"]
        verification_results = []

        for zone_key in zones:
            if position_manager.is_recorded(zone_key):
                zone_name = position_manager.get_zone_display_name(zone_key)
                expected_position = position_manager.get_position(zone_key)

                self.log(f"🎯 验证{zone_name}...", "cyan")

                # 移动到位置
                if self.slide_controller.is_connected:
                    success = self.slide_controller.move_to_absolute_position(expected_position)
                else:
                    distance_to_move = expected_position - self.current_conveyor_position
                    cmd = f"MovJExt({distance_to_move},{{SYNC=1}})"
                    success = send_cmd(self.motion_socket, cmd, "MOT")

                if success:
                    self.current_conveyor_position = expected_position
                    actual_position = self.current_conveyor_position
                    is_valid, error = position_manager.validate_position(zone_key, actual_position)

                    verification_results.append({
                        "zone": zone_name,
                        "expected": expected_position,
                        "actual": actual_position,
                        "error": error,
                        "valid": is_valid
                    })

                    if is_valid:
                        self.log(f"✅ {zone_name}: 误差 {error:.2f}mm", "green")
                    else:
                        self.log(f"⚠️ {zone_name}: 误差 {error:.2f}mm (超出容差)", "orange")
                else:
                    self.log(f"❌ {zone_name}: 移动失败", "red")
                    verification_results.append({
                        "zone": zone_name,
                        "expected": expected_position,
                        "actual": None,
                        "error": float('inf'),
                        "valid": False
                    })

                # 等待移动完成
                time.sleep(1)

        # 显示验证摘要
        valid_count = sum(1 for r in verification_results if r["valid"])
        total_count = len(verification_results)

        if valid_count == total_count:
            self.log(f"🎉 所有位置验证通过！({valid_count}/{total_count})", "lightgreen")
            self.label_verify_result.configure(
                text=f"🎉 批量验证完成：{valid_count}/{total_count} 通过",
                text_color="green"
            )
        else:
            self.log(f"⚠️ 位置验证完成：{valid_count}/{total_count} 通过", "orange")
            self.label_verify_result.configure(
                text=f"⚠️ 批量验证完成：{valid_count}/{total_count} 通过",
                text_color="orange"
            )

        self.update_conveyor_display()

    def clear_all_records(self):
        """清除所有位置记录"""
        self.log("🗑️ 清除所有位置记录...", "yellow")
        position_manager.clear_all_records()
        self.update_position_record_display()
        self.label_verify_result.configure(text="")
        self.log("✅ 所有位置记录已清除", "green")

    def export_positions(self):
        """导出位置数据"""
        success = position_manager.export_positions()
        if success:
            self.log("✅ 位置数据导出成功", "green")
        else:
            self.log("❌ 位置数据导出失败", "red")
    # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

    # ▼▼▼▼▼ 【工件分拣控制功能 - 新增】 ▼▼▼▼▼
    def sync_door_coordinates(self):
        """同步门坐标到传送带pro项目"""
        # 检查门坐标是否都已记录
        door1_pos = position_manager.get_position("door_coord_1")
        door2_pos = position_manager.get_position("door_coord_2")

        if door1_pos is None or door2_pos is None:
            self.log("❌ 请先记录门坐标1和门坐标2", "red")
            self.label_sorting_info.configure(
                text="❌ 门坐标未完整记录，请先记录门坐标1和门坐标2",
                text_color="red"
            )
            return False

        self.log("🔄 正在同步门坐标到传送带pro项目...", "cyan")

        try:
            # 需要更新的文件列表
            lua_files = [
                os.path.join(self.conveyor_pro_project_path, "global.lua"),
                os.path.join(self.conveyor_pro_project_path, "src0.lua")
            ]

            for lua_file_path in lua_files:
                if not os.path.exists(lua_file_path):
                    self.log(f"❌ 找不到文件: {lua_file_path}", "red")
                    continue

                with open(lua_file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 使用正则表达式替换传送带位置参数
                # 替换CONVEYOR_DETECTION1_POS
                pattern1 = r'local CONVEYOR_DETECTION1_POS = \d+\.?\d*'
                replacement1 = f'local CONVEYOR_DETECTION1_POS = {door1_pos:.2f}'
                content = re.sub(pattern1, replacement1, content)

                # 替换CONVEYOR_DETECTION2_POS
                pattern2 = r'local CONVEYOR_DETECTION2_POS = \d+\.?\d*'
                replacement2 = f'local CONVEYOR_DETECTION2_POS = {door2_pos:.2f}'
                content = re.sub(pattern2, replacement2, content)

                # 写回文件
                with open(lua_file_path, 'w', encoding='utf-8') as f:
                    f.write(content)

                self.log(f"✅ 已更新文件: {os.path.basename(lua_file_path)}", "green")

            self.log(f"✅ 门坐标同步完成！", "green")
            self.log(f"   门坐标1 (检测区1): {door1_pos:.2f} mm", "green")
            self.log(f"   门坐标2 (检测区2): {door2_pos:.2f} mm", "green")

            self.label_sorting_info.configure(
                text=f"✅ 门坐标已同步 - 门坐标1: {door1_pos:.2f}mm, 门坐标2: {door2_pos:.2f}mm",
                text_color="green"
            )

            return True

        except Exception as e:
            self.log(f"❌ 同步门坐标失败: {e}", "red")
            self.label_sorting_info.configure(
                text=f"❌ 同步失败: {str(e)}",
                text_color="red"
            )
            return False

    def start_material_sorting(self):
        """启动工件分拣程序"""
        if self.is_sorting_running:
            self.log("⚠️ 分拣程序已在运行中", "orange")
            return

        # 检查门坐标是否已记录
        if not position_manager.is_all_recorded():
            self.log("❌ 请先记录完整的门坐标", "red")
            self.label_sorting_info.configure(
                text="❌ 请先记录门坐标1和门坐标2",
                text_color="red"
            )
            return

        # 先同步门坐标
        if not self.sync_door_coordinates():
            return

        self.log("🚀 正在启动工件分拣程序...", "cyan")

        try:
            # 检查机器人连接状态
            if not self.is_robot_connected:
                self.log("❌ 请先连接机器人", "red")
                return

            self.log("🚀 启动工件分拣程序...", "cyan")

            # 启动分拣循环线程
            self.is_sorting_running = True
            self.label_sorting_status.configure(text="运行中", text_color="green")
            self.btn_start_sorting.configure(state="disabled")
            self.btn_stop_sorting.configure(state="normal")
            self.label_sorting_info.configure(
                text="✅ 工件分拣程序已启动，正在执行自动分拣流程...",
                text_color="green"
            )

            # 启动分拣循环线程
            sorting_thread = threading.Thread(target=self.sorting_loop_thread, daemon=True)
            sorting_thread.start()

            self.log("✅ 工件分拣程序启动成功！", "green")

        except Exception as e:
            self.log(f"❌ 启动分拣程序时发生错误: {e}", "red")
            self.label_sorting_info.configure(
                text=f"❌ 启动失败: {str(e)}",
                text_color="red"
            )

    def stop_material_sorting(self):
        """停止工件分拣程序"""
        if not self.is_sorting_running:
            self.log("⚠️ 分拣程序未在运行", "orange")
            return

        self.log("🛑 正在停止工件分拣程序...", "yellow")

        try:
            # 通过HTTP API停止传送带pro项目
            success = self.stop_conveyor_pro_project()

            if success:
                self.is_sorting_running = False
                self.label_sorting_status.configure(text="已停止", text_color="orange")
                self.btn_start_sorting.configure(state="normal")
                self.btn_stop_sorting.configure(state="disabled")
                self.label_sorting_info.configure(
                    text="🛑 工件分拣程序已停止",
                    text_color="orange"
                )
                self.log("✅ 工件分拣程序已停止", "orange")
            else:
                self.log("❌ 停止工件分拣程序失败", "red")

        except Exception as e:
            self.log(f"❌ 停止分拣程序时发生错误: {e}", "red")

    def check_dobot_studio_running(self):
        """检查DobotStudio Pro是否正在运行"""
        try:
            # 尝试连接到DobotStudio Pro的HTTP接口
            response = requests.get(f"http://{ROBOT_IP}:22000/connection/state", timeout=3)
            return response.status_code == 200
        except:
            return False

    def start_conveyor_pro_project(self):
        """启动传送带pro项目"""
        try:
            self.log("🚀 正在启动传送带pro自动分拣程序...", "cyan")

            # 方法1: 直接通过DobotStudio Pro命令行启动项目
            project_path = os.path.abspath(self.conveyor_pro_project_path)

            # 检查项目文件是否存在
            prj_file = os.path.join(project_path, "prj.json")
            if not os.path.exists(prj_file):
                self.log(f"❌ 项目文件不存在: {prj_file}", "red")
                return False

            # 方法2: 通过TCP直接发送Lua脚本执行指令
            if self.motion_socket:
                self.log("📡 通过TCP启动分拣流程...", "cyan")

                # 发送初始化指令
                init_commands = [
                    "DO(2, 0)",  # 关闭气泵
                    "DO(1, 1)",  # 打开吸盘
                ]

                for cmd in init_commands:
                    success = send_cmd(self.motion_socket, cmd, "INIT")
                    if not success:
                        self.log(f"❌ 初始化指令失败: {cmd}", "red")
                        return False

                self.log("✅ 传送带pro分拣程序启动成功！", "green")
                self.log("🔄 分拣程序将开始自动循环执行...", "cyan")
                return True
            else:
                self.log("❌ 机器人未连接，无法启动分拣程序", "red")
                return False

        except Exception as e:
            self.log(f"❌ 启动传送带pro项目失败: {e}", "red")
            return False

    def stop_conveyor_pro_project(self):
        """停止传送带pro项目"""
        try:
            self.log("🛑 正在停止传送带pro分拣程序...", "yellow")

            if self.motion_socket:
                # 发送暂停指令
                cmd = "Pause()"
                success = send_cmd(self.motion_socket, cmd, "STOP")

                if success:
                    # 安全停止：关闭所有IO
                    safety_commands = [
                        "DO(1, 0)",  # 关闭吸盘
                        "DO(2, 0)",  # 关闭气泵
                    ]

                    for cmd in safety_commands:
                        send_cmd(self.motion_socket, cmd, "SAFE")

                    self.log("✅ 传送带pro分拣程序已安全停止", "orange")
                    return True
                else:
                    self.log("❌ 停止指令发送失败", "red")
                    return False
            else:
                self.log("❌ 机器人未连接，无法发送停止指令", "red")
                return False

        except Exception as e:
            self.log(f"❌ 停止传送带pro项目失败: {e}", "red")
            return False

    def start_sorting_monitor(self):
        """启动分拣监控线程"""
        def monitor_sorting():
            while self.is_sorting_running:
                try:
                    # 检查项目运行状态
                    if not self.check_dobot_studio_running():
                        self.log("⚠️ 检测到DobotStudio Pro连接中断", "orange")
                        self.is_sorting_running = False
                        self.label_sorting_status.configure(text="连接中断", text_color="red")
                        self.btn_start_sorting.configure(state="normal")
                        self.btn_stop_sorting.configure(state="disabled")
                        break

                    time.sleep(2)  # 每2秒检查一次
                except Exception as e:
                    self.log(f"监控线程错误: {e}", "red")
                    break

        monitor_thread = threading.Thread(target=monitor_sorting, daemon=True)
        monitor_thread.start()

    def execute_sorting_cycle(self):
        """执行一次完整的分拣循环（基于传送带pro逻辑）"""
        if not self.is_robot_connected:
            self.log("❌ 机器人未连接，无法执行分拣", "red")
            return False

        try:
            self.log("🔄 开始执行分拣循环...", "cyan")

            # 获取门坐标位置
            door1_pos = position_manager.get_position("door_coord_1")  # 对应检测区1
            door2_pos = position_manager.get_position("door_coord_2")  # 对应检测区2

            if door1_pos is None or door2_pos is None:
                self.log("❌ 门坐标未记录完整", "red")
                return False

            # 步骤1: 移动到检测区1进行物料识别
            self.log("📍 步骤1: 移动传送带到门坐标1(检测区1)...", "cyan")
            if self.slide_controller.is_connected:
                success = self.slide_controller.move_to_absolute_position(door1_pos)
            else:
                distance_to_move = door1_pos - self.current_conveyor_position
                cmd = f"MovJExt({distance_to_move},{{SYNC=1}})"
                success = send_cmd(self.motion_socket, cmd, "MOT")

            if success:
                self.current_conveyor_position = door1_pos
                self.update_conveyor_display()
                self.log(f"✅ 传送带已到达门坐标1: {door1_pos:.2f} mm", "green")

                # 步骤2: 触发检测区1视觉检测
                self.log("👁️ 步骤2: 触发检测区1视觉检测...", "cyan")
                self.trigger_vision_capture()

                # 等待视觉检测结果
                time.sleep(2)

                # 步骤3: 移动到检测区2
                self.log("📍 步骤3: 移动传送带到门坐标2(检测区2)...", "cyan")
                if self.slide_controller.is_connected:
                    success = self.slide_controller.move_to_absolute_position(door2_pos)
                else:
                    distance_to_move = door2_pos - self.current_conveyor_position
                    cmd = f"MovJExt({distance_to_move},{{SYNC=1}})"
                    success = send_cmd(self.motion_socket, cmd, "MOT")

                if success:
                    self.current_conveyor_position = door2_pos
                    self.update_conveyor_display()
                    self.log(f"✅ 传送带已到达门坐标2: {door2_pos:.2f} mm", "green")
                    self.log("✅ 分拣循环执行完成", "green")
                    return True
                else:
                    self.log("❌ 移动到门坐标2失败", "red")
                    return False
            else:
                self.log("❌ 移动到门坐标1失败", "red")
                return False

        except Exception as e:
            self.log(f"❌ 执行分拣循环时发生错误: {e}", "red")
            return False

    def sorting_loop_thread(self):
        """分拣循环线程（基于传送带pro的逻辑）"""
        cycle_count = 0

        try:
            # 初始化IO
            self.log("🔧 初始化分拣系统...", "cyan")
            send_cmd(self.motion_socket, "DO(2, 0)", "INIT")  # 关闭气泵
            send_cmd(self.motion_socket, "DO(1, 1)", "INIT")  # 打开吸盘

            while self.is_sorting_running:
                cycle_count += 1
                self.log(f"🔄 开始第 {cycle_count} 次分拣循环", "cyan")

                # 获取门坐标位置
                door1_pos = position_manager.get_position("door_coord_1")  # 检测区1
                door2_pos = position_manager.get_position("door_coord_2")  # 检测区2

                # 步骤1: 移动到检测区1
                self.log("📍 移动到门坐标1(检测区1)进行物料识别...", "cyan")
                if self.slide_controller.is_connected:
                    success = self.slide_controller.move_to_absolute_position(door1_pos)
                else:
                    distance_to_move = door1_pos - self.current_conveyor_position
                    cmd = f"MovJExt({distance_to_move},{{SYNC=1}})"
                    success = send_cmd(self.motion_socket, cmd, "MOT")

                if success:
                    self.current_conveyor_position = door1_pos
                    self.update_conveyor_display()

                    # 步骤2: 触发视觉检测
                    self.log("👁️ 触发检测区1视觉检测...", "cyan")
                    self.trigger_vision_capture()

                    # 等待视觉处理
                    time.sleep(3)

                    # 步骤3: 移动到检测区2
                    self.log("📍 移动到门坐标2(检测区2)进行背面检测...", "cyan")
                    if self.slide_controller.is_connected:
                        success = self.slide_controller.move_to_absolute_position(door2_pos)
                    else:
                        distance_to_move = door2_pos - self.current_conveyor_position
                        cmd = f"MovJExt({distance_to_move},{{SYNC=1}})"
                        success = send_cmd(self.motion_socket, cmd, "MOT")

                    if success:
                        self.current_conveyor_position = door2_pos
                        self.update_conveyor_display()
                        self.log(f"✅ 第 {cycle_count} 次分拣循环完成", "green")
                    else:
                        self.log("❌ 移动到检测区2失败", "red")
                else:
                    self.log("❌ 移动到检测区1失败", "red")

                # 循环间隔
                if self.is_sorting_running:
                    time.sleep(2)

        except Exception as e:
            self.log(f"❌ 分拣循环线程错误: {e}", "red")
        finally:
            # 清理工作
            self.is_sorting_running = False
            self.label_sorting_status.configure(text="已停止", text_color="orange")
            self.btn_start_sorting.configure(state="normal")
            self.btn_stop_sorting.configure(state="disabled")
            self.log("🛑 分拣循环线程已退出", "orange")
    # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲
    
    # (其他函数无变化...)
    def log(self, message, color="white"):
        self.log_textbox.configure(state="normal"); self.log_textbox.tag_config(f"tag_{color}", foreground=color); self.log_textbox.insert("end", f"{message}\n", f"tag_{color}"); self.log_textbox.configure(state="disabled"); self.log_textbox.see("end")
    def handle_connect_button_click(self):
        if not self.is_robot_connected:
            try:
                self.log(f"正在连接机器人 at {ROBOT_IP}...", "cyan"); self.dashboard_socket = socket.create_connection((ROBOT_IP, DASHBOARD_PORT), timeout=5); self.motion_socket = socket.create_connection((ROBOT_IP, MOTION_PORT), timeout=5)
                self.log("正在尝试使能机器人...", "yellow")
                if not send_cmd(self.dashboard_socket, "EnableRobot()", "DASH"): raise ConnectionError("机器人使能失败")
                self.log("机器人已使能，等待伺服系统稳定...", "yellow"); time.sleep(1)
                self.is_robot_connected = True; self.log("✅ 机器人连接并使能成功!", "green"); self.connect_label.configure(text="机器人已连接", text_color="green"); self.btn_connect.configure(text="断开连接")
            except Exception as e:
                self.log(f"❌ 连接失败: {e}", "red")
                if self.dashboard_socket: self.dashboard_socket.close()
                if self.motion_socket: self.motion_socket.close()
        else:
            send_cmd(self.dashboard_socket, "DisableRobot()", "DASH");
            if self.dashboard_socket: self.dashboard_socket.close();
            if self.motion_socket: self.motion_socket.close();
            self.is_robot_connected = False; self.log("🔌 机器人已断开。", "orange"); self.connect_label.configure(text="机器人未连接", text_color="orange"); self.btn_connect.configure(text="连接机器人")
    def trigger_vision_capture(self):
        self.log("📸 发送拍照触发指令...", "yellow")
        try:
            with socket.create_connection((PYTHON_PC_IP, VISION_TRIGGER_PORT), timeout=3) as s:
                cmd_to_send = VISION_TRIGGER_CMD + "\n"; s.sendall(cmd_to_send.encode('utf-8')); self.log("✅ 触发指令已发送成功。", "green")
        except socket.timeout: self.log(f"❌ 触发失败: 连接视觉软件({PYTHON_PC_IP}:{VISION_TRIGGER_PORT})超时。", "red")
        except Exception as e: self.log(f"❌ 触发失败: {e}", "red")
    def start_jog(self, axis_id):
        if not self.is_robot_connected: self.log("⚠️ 请先连接机器人", "orange"); return
        self.log(f"🤖 开始点动: {axis_id}", "cyan"); send_cmd(self.motion_socket, f"MoveJog({axis_id})", "MOT")
    def stop_jog(self, event=None):
        if not self.is_robot_connected: return
        self.log("🤖 停止点动", "cyan"); send_cmd(self.motion_socket, "MoveJog()", "MOT"); time.sleep(0.2)
    def go_home(self):
        if not self.is_robot_connected: self.log("⚠️ 请先连接机器人", "orange"); return
        self.log("🤖 正在移动到安全原点...");
        if send_cmd(self.motion_socket, "MoveJ(200, 0, 50, 0)", "MOT"):
            send_cmd(self.dashboard_socket, "Sync()", "DASH"); self.log("✅ 已到达原点。")
        else: self.log("❌ 回原点失败。", "red")
    def execute_pick_and_place(self, target_x, target_y, target_r):
        if not self.is_robot_connected: self.log("⚠️ 自动抓取失败：机器人未连接", "orange"); return
        self.log(f"🤖 开始执行抓取任务..."); pickup_z_high, pickup_z_low = 50, 10; place_x, place_y, place_z = 150, -150, 50
        try:
            send_cmd(self.motion_socket, f"DO({GRIPPER_IO_INDEX}, 0)", "MOT"); send_cmd(self.motion_socket, f"MoveJ({target_x}, {target_y}, {pickup_z_high}, {target_r})", "MOT"); send_cmd(self.dashboard_socket, "Sync()", "DASH")
            send_cmd(self.motion_socket, f"MoveL({target_x}, {target_y}, {pickup_z_low}, {target_r})", "MOT"); send_cmd(self.dashboard_socket, "Sync()", "DASH"); self.log("🤏 抓取: 闭合夹爪", "cyan")
            send_cmd(self.motion_socket, f"DO({GRIPPER_IO_INDEX}, 1)", "MOT"); send_cmd(self.dashboard_socket, "Sync()", "DASH") 
            send_cmd(self.motion_socket, f"MoveL({target_x}, {target_y}, {pickup_z_high}, {target_r})", "MOT"); send_cmd(self.dashboard_socket, "Sync()", "DASH")
            send_cmd(self.motion_socket, f"MoveJ({place_x}, {place_y}, {place_z}, 0)", "MOT"); send_cmd(self.dashboard_socket, "Sync()", "DASH"); self.log("👐 放置: 张开夹爪", "cyan")
            send_cmd(self.motion_socket, f"DO({GRIPPER_IO_INDEX}, 0)", "MOT"); send_cmd(self.dashboard_socket, "Sync()", "DASH")
            self.go_home(); self.log("✅ 抓取任务完成!", "green")
        except Exception as e: self.log(f"❌ 机器人执行动作时出错: {e}", "red")
    def process_vision_queue(self):
        try:
            while not self.vision_queue.empty():
                message = self.vision_queue.get_nowait()
                self.log(f"📩 收到视觉数据包: {message}", "cyan")
                parts = message.split(';')
                image_path = parts[-1].strip()
                self.show_image_from_path(image_path)
                if len(parts) >= 2 and self.auto_run_switch.get() and self.is_robot_connected:
                    coord_data = parts[0]
                    try:
                        coord_parts = coord_data.split(',');
                        if len(coord_parts) >= 2:
                            robot_x, robot_y = float(coord_parts[0]), float(coord_parts[1]); robot_r = float(coord_parts[2]) if len(coord_parts) > 2 else 0.0
                            self.execute_pick_and_place(robot_x, robot_y, robot_r)
                        else: self.log(f"⚠️ 坐标部分格式无法解析: {coord_data}", "orange")
                    except (ValueError, IndexError) as e: self.log(f"❌ 解析坐标数据失败: {e}", "red")
        except queue.Empty: pass
        self.after(100, self.process_vision_queue)
    def show_image_from_path(self, image_path):
        max_retries = 5; retry_delay = 0.2
        for attempt in range(max_retries):
            if os.path.exists(image_path):
                try:
                    with Image.open(image_path) as image: image.verify()
                    with Image.open(image_path) as image:
                        image.thumbnail((self.image_display_label.winfo_width(), self.image_display_label.winfo_height()), Image.Resampling.LANCZOS)
                        ctk_image = ImageTk.PhotoImage(image)
                        self.image_display_label.configure(image=ctk_image, text=""); self.image_display_label.image = ctk_image
                        self.log(f"✅ 图像显示成功。(尝试第 {attempt + 1} 次)", "green"); return
                except (IOError, SyntaxError) as e: self.log(f"   - 第 {attempt + 1} 次尝试：文件不完整 ({e})，稍后重试...", "yellow"); time.sleep(retry_delay)
                except PermissionError: self.log(f"   - 第 {attempt + 1} 次尝试：文件被占用，稍后重试...", "yellow"); time.sleep(retry_delay)
                except Exception as e: self.log(f"❌ 显示图像时发生未知错误: {e}", "red"); return
            else: self.log(f"❌ 找不到图像文件: {image_path}", "red"); return
        self.log(f"❌ 图像加载失败：在 {max_retries} 次尝试后依然无法读取文件。", "red")
    def vision_listener_thread(self):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            try:
                s.bind(('0.0.0.0', VISION_SERVER_PORT)); s.listen()
                print(f"👂 视觉服务器已启动，正在监听端口 {VISION_SERVER_PORT}...")
                while True:
                    conn, addr = s.accept()
                    with conn:
                        print(f"🤝 视觉软件已连接: {addr}")
                        while True:
                            data = conn.recv(1024)
                            if not data: print("🔌 视觉软件已断开。"); break
                            self.vision_queue.put(data.decode('utf-8').strip())
            except OSError as e: print(f"❌ 端口 {VISION_SERVER_PORT} 绑定失败: {e}")
    def on_closing(self):
        if self.is_robot_connected:
            self.log("正在断开机器人连接..."); send_cmd(self.dashboard_socket, "DisableRobot()", "DASH")
            if self.dashboard_socket: self.dashboard_socket.close();
            if self.motion_socket: self.motion_socket.close()
        self.destroy()

if __name__ == "__main__":
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    app = RobotControlApp()
    app.mainloop()